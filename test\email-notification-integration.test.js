/**
 * Integration test for email notification functionality
 * This test verifies the complete email notification workflow
 */

const request = require('supertest');
const app = require('../app'); // Assuming your Express app is exported from app.js
const mongoose = require('mongoose');
const User = require('../src/models/User');
const Company = require('../src/models/Company');
const Test = require('../src/models/Test');
const emailNotificationService = require('../src/services/emailNotificationService');

// Mock email services to avoid sending actual emails during tests
jest.mock('../src/services/mailServices', () => ({
    sendTestCreatedNotification: jest.fn().mockResolvedValue({
        success: true,
        messageId: 'test-message-id'
    }),
    sendBulkTestAssignmentNotification: jest.fn().mockResolvedValue([
        { candidateEmail: '<EMAIL>', success: true, messageId: 'msg1' }
    ])
}));

describe('Email Notification Integration Tests', () => {
    let companyUser, candidateUser, company, authToken;

    beforeAll(async () => {
        // Connect to test database
        if (mongoose.connection.readyState === 0) {
            await mongoose.connect(process.env.MONGODB_TEST_URI || 'mongodb://localhost:27017/resumebuilder_test');
        }
    });

    beforeEach(async () => {
        // Clean up database
        await User.deleteMany({});
        await Company.deleteMany({});
        await Test.deleteMany({});

        // Create test company user
        companyUser = await User.create({
            name: 'Test Company',
            email: '<EMAIL>',
            password: 'hashedpassword123',
            role: 'company',
            isVerified: true
        });

        // Create test candidate user
        candidateUser = await User.create({
            name: 'Test Candidate',
            email: '<EMAIL>',
            password: 'hashedpassword123',
            role: 'candidate',
            isVerified: true
        });

        // Create company profile
        company = await Company.create({
            userId: companyUser._id,
            companyName: 'Tech Solutions Inc',
            email: '<EMAIL>',
            industry: 'Technology'
        });

        // Mock authentication token (you'll need to implement this based on your auth system)
        authToken = 'mock-jwt-token';
    });

    afterAll(async () => {
        await mongoose.connection.close();
    });

    describe('Test Creation Email Notifications', () => {
        it('should send email notification when test is created', async () => {
            const testData = {
                testName: 'JavaScript Assessment',
                description: 'Test for JavaScript skills',
                duration: 60,
                passingScore: 70,
                scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // Next week
                questions: [
                    {
                        questionId: new mongoose.Types.ObjectId(),
                        points: 5
                    }
                ],
                instructions: 'Please complete all questions carefully',
                allowedAttempts: 1,
                randomizeQuestions: false,
                showResults: true
            };

            // Mock the authentication middleware to set req.user
            const originalAuth = require('../src/middlewares/authMiddleware');
            jest.doMock('../src/middlewares/authMiddleware', () => (req, res, next) => {
                req.user = { id: companyUser._id };
                next();
            });

            const response = await request(app)
                .post('/api/tests')
                .set('Authorization', `Bearer ${authToken}`)
                .send(testData)
                .expect(201);

            expect(response.body.success).toBe(true);
            expect(response.body.test).toBeDefined();

            // Verify that email notification service was called
            const { sendTestCreatedNotification } = require('../src/services/mailServices');
            expect(sendTestCreatedNotification).toHaveBeenCalledWith(
                companyUser.email,
                expect.objectContaining({
                    testName: 'JavaScript Assessment',
                    companyName: 'Tech Solutions Inc'
                })
            );
        });
    });

    describe('Candidate Assignment Email Notifications', () => {
        let test;

        beforeEach(async () => {
            // Create a test first
            test = await Test.create({
                testName: 'React Assessment',
                companyId: company._id,
                description: 'Test for React skills',
                duration: 90,
                passingScore: 75,
                scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                questions: [
                    {
                        questionId: new mongoose.Types.ObjectId(),
                        points: 10
                    }
                ],
                participants: []
            });
        });

        it('should send email notifications when candidates are assigned to test', async () => {
            const assignmentData = {
                candidateIds: [candidateUser._id.toString()]
            };

            // Mock the authentication and role middleware
            jest.doMock('../src/middlewares/authMiddleware', () => (req, res, next) => {
                req.user = { id: companyUser._id };
                next();
            });

            const response = await request(app)
                .post(`/api/tests/${test._id}/assign-candidates`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(assignmentData)
                .expect(200);

            expect(response.body.success).toBe(true);

            // Verify that bulk email notification service was called
            const { sendBulkTestAssignmentNotification } = require('../src/services/mailServices');
            expect(sendBulkTestAssignmentNotification).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({
                        candidateEmail: candidateUser.email,
                        candidateName: candidateUser.name,
                        testName: 'React Assessment'
                    })
                ])
            );
        });
    });

    describe('Email Configuration Validation', () => {
        it('should validate email configuration', async () => {
            // Mock successful email validation
            jest.spyOn(emailNotificationService, 'validateEmailConfiguration')
                .mockResolvedValue({
                    success: true,
                    message: 'Email configuration is valid'
                });

            const response = await request(app)
                .get('/api/tests/email/validate')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(200);

            expect(response.body.success).toBe(true);
            expect(response.body.message).toBe('Email configuration is valid and working');
        });

        it('should handle email configuration validation failure', async () => {
            // Mock failed email validation
            jest.spyOn(emailNotificationService, 'validateEmailConfiguration')
                .mockResolvedValue({
                    success: false,
                    error: 'SMTP authentication failed'
                });

            const response = await request(app)
                .get('/api/tests/email/validate')
                .set('Authorization', `Bearer ${authToken}`)
                .expect(500);

            expect(response.body.success).toBe(false);
            expect(response.body.message).toBe('Email configuration validation failed');
        });
    });

    describe('Email Notification Error Handling', () => {
        it('should not fail test creation if email notification fails', async () => {
            // Mock email service failure
            const { sendTestCreatedNotification } = require('../src/services/mailServices');
            sendTestCreatedNotification.mockResolvedValue({
                success: false,
                error: 'SMTP server unavailable'
            });

            const testData = {
                testName: 'Error Test',
                duration: 30,
                scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
                endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
                questions: [
                    {
                        questionId: new mongoose.Types.ObjectId(),
                        points: 5
                    }
                ]
            };

            const response = await request(app)
                .post('/api/tests')
                .set('Authorization', `Bearer ${authToken}`)
                .send(testData)
                .expect(201);

            // Test should still be created successfully
            expect(response.body.success).toBe(true);
            expect(response.body.test).toBeDefined();
        });
    });
});
