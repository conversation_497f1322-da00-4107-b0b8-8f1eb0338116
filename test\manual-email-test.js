/**
 * Manual test script for email notification functionality
 * Run this script to test email notifications manually
 * 
 * Usage: node test/manual-email-test.js
 */

const emailNotificationService = require('../src/services/emailNotificationService');
const { sendTestLinkEmail } = require('../src/services/mailServices');

async function testEmailConfiguration() {
    console.log('🔧 Testing email configuration...');
    
    try {
        const result = await emailNotificationService.validateEmailConfiguration();
        
        if (result.success) {
            console.log('✅ Email configuration is valid');
            return true;
        } else {
            console.log('❌ Email configuration failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Email configuration test failed:', error.message);
        return false;
    }
}

async function testTestCreationNotification() {
    console.log('\n📧 Testing test creation notification...');
    
    const mockCompanyUser = {
        email: '<EMAIL>', // Change this to your test email
        name: 'Test Company User'
    };

    const mockTestDetails = {
        testName: 'Sample JavaScript Assessment',
        testId: '507f1f77bcf86cd799439011',
        totalQuestions: 15,
        duration: 60,
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // Next week
    };

    const mockCompany = {
        companyName: 'Tech Solutions Inc',
        name: 'Tech Solutions Inc'
    };

    try {
        const result = await emailNotificationService.sendTestCreationNotification(
            mockCompanyUser,
            mockTestDetails,
            mockCompany
        );

        if (result.success) {
            console.log('✅ Test creation notification sent successfully');
            console.log('   Message ID:', result.messageId);
            return true;
        } else {
            console.log('❌ Test creation notification failed:', result.message);
            console.log('   Reason:', result.reason);
            console.log('   Error:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Test creation notification test failed:', error.message);
        return false;
    }
}

async function testCandidateAssignmentNotification() {
    console.log('\n👥 Testing candidate assignment notification...');
    
    // Mock test data
    const mockTest = {
        _id: '507f1f77bcf86cd799439011',
        testName: 'React Developer Assessment',
        duration: 90,
        instructions: 'Please complete all questions within the time limit. Ensure you have a stable internet connection.',
        scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
    };

    const mockCompany = {
        companyName: 'Innovative Tech Corp',
        name: 'Innovative Tech Corp'
    };

    // Test with a single candidate email (change this to your test email)
    const testCandidateEmail = '<EMAIL>';
    const testUrl = `https://localhost:5173/testapplication/${mockTest._id}`;

    try {
        const result = await sendTestLinkEmail(testCandidateEmail, testUrl, {
            testName: mockTest.testName,
            duration: `${mockTest.duration} minutes`,
            instructions: mockTest.instructions,
            expiryTime: new Date(mockTest.endDate).toLocaleDateString(),
            candidateName: 'Test Candidate',
            companyName: mockCompany.companyName
        });

        if (result.success) {
            console.log('✅ Candidate assignment notification sent successfully');
            console.log('   Message ID:', result.messageId);
            console.log('   Test URL:', testUrl);
            return true;
        } else {
            console.log('❌ Candidate assignment notification failed:', result.error);
            return false;
        }
    } catch (error) {
        console.log('❌ Candidate assignment notification test failed:', error.message);
        return false;
    }
}

async function testBulkCandidateNotifications() {
    console.log('\n📬 Testing bulk candidate notifications...');
    
    const mockAssignments = [
        {
            candidateEmail: '<EMAIL>', // Change to your test emails
            candidateName: 'John Doe',
            testId: '507f1f77bcf86cd799439011',
            testName: 'Full Stack Developer Assessment',
            duration: 120,
            instructions: 'This is a comprehensive assessment covering both frontend and backend skills.',
            companyName: 'Future Tech Solutions',
            scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
            endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        },
        {
            candidateEmail: '<EMAIL>', // Change to your test emails
            candidateName: 'Jane Smith',
            testId: '507f1f77bcf86cd799439011',
            testName: 'Full Stack Developer Assessment',
            duration: 120,
            instructions: 'This is a comprehensive assessment covering both frontend and backend skills.',
            companyName: 'Future Tech Solutions',
            scheduledDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
            endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
        }
    ];

    try {
        const { sendBulkTestAssignmentNotification } = require('../src/services/mailServices');
        const results = await sendBulkTestAssignmentNotification(mockAssignments);

        const successCount = results.filter(r => r.success).length;
        const failCount = results.filter(r => !r.success).length;

        console.log(`✅ Bulk notifications completed: ${successCount} successful, ${failCount} failed`);
        
        results.forEach((result, index) => {
            if (result.success) {
                console.log(`   ✅ ${mockAssignments[index].candidateEmail}: ${result.messageId}`);
            } else {
                console.log(`   ❌ ${result.candidateEmail}: ${result.error}`);
            }
        });

        return successCount > 0;
    } catch (error) {
        console.log('❌ Bulk candidate notifications test failed:', error.message);
        return false;
    }
}

async function runAllTests() {
    console.log('🚀 Starting Email Notification Manual Tests\n');
    console.log('⚠️  Make sure to update the test email addresses in this script before running!\n');

    const results = [];

    // Test 1: Email Configuration
    results.push(await testEmailConfiguration());

    // Test 2: Test Creation Notification
    results.push(await testTestCreationNotification());

    // Test 3: Single Candidate Assignment
    results.push(await testCandidateAssignmentNotification());

    // Test 4: Bulk Candidate Notifications
    results.push(await testBulkCandidateNotifications());

    // Summary
    const passedTests = results.filter(r => r).length;
    const totalTests = results.length;

    console.log('\n📊 Test Summary:');
    console.log(`   Passed: ${passedTests}/${totalTests}`);
    console.log(`   Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

    if (passedTests === totalTests) {
        console.log('\n🎉 All email notification tests passed!');
    } else {
        console.log('\n⚠️  Some tests failed. Please check the configuration and try again.');
    }

    process.exit(passedTests === totalTests ? 0 : 1);
}

// Run the tests
if (require.main === module) {
    runAllTests().catch(error => {
        console.error('❌ Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = {
    testEmailConfiguration,
    testTestCreationNotification,
    testCandidateAssignmentNotification,
    testBulkCandidateNotifications
};
