const nodemailer = require("nodemailer");

const transporter = nodemailer.createTransport({
  host: "smtp.gmail.com",
  port: 587,
  secure: false,
  auth: {
    user: "<EMAIL>",
    pass: "towb ekvx nott fzbd",
  },
});

const LOGO_URL = "https://res.cloudinary.com/dhqwyqekj/image/upload/w_1000,c_fill,ar_1:1,g_auto,r_max,bo_5px_solid_red,b_rgb:262c35/v1750831228/Medini_logo_White-1_1_kfnj8n.png";
const BRAND_NAME = "Resumeduilder";
// const BRAND_NAME = process.env.BRAND_NAME || "ResumeBuilder Pro";
// const LOGO_URL = process.env.LOGO_URL || "https://yourapp.com/logo.png";
const SUPPORT_EMAIL = process.env.SUPPORT_EMAIL || "<EMAIL>";
const COMPANY_ADDRESS = process.env.COMPANY_ADDRESS || "123 Business St, City, State 12345";

function createEmailTemplate({ title, message, buttonUrl, buttonText, additionalInfo }) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${title}</title>
</head>
<body style="margin:0;padding:0;background-color:#f4f6f9;font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
<table role="presentation" width="100%" cellpadding="0" cellspacing="0" style="background-color:#f4f6f9;">
<tr><td align="center" style="padding:20px 0;">
  <table role="presentation" width="600" cellpadding="0" cellspacing="0" style="background-color:#ffffff;border-radius:12px;box-shadow:0 4px 12px rgba(0,0,0,0.1);overflow:hidden;max-width:600px;">
    
    <!-- Header with Logo -->
    <tr>
      <td align="center" style="padding:40px 30px 20px 30px;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <img src="${LOGO_URL}" alt="${BRAND_NAME}" style="width:140px;height:auto;margin-bottom:10px;" />
        <h1 style="color:#ffffff;font-size:28px;margin:0;font-weight:300;letter-spacing:1px;">${BRAND_NAME}</h1>
      </td>
    </tr>
    
    <!-- Main Content -->
    <tr>
      <td style="padding:40px 40px 20px 40px;">
        <h2 style="color:#2c3e50;font-size:24px;margin:0 0 20px 0;font-weight:600;text-align:center;">
          ${title}
        </h2>
      </td>
    </tr>
    
    <tr>
      <td style="padding:0 40px 30px 40px;">
        <div style="color:#555555;font-size:16px;line-height:1.6;text-align:left;">
          ${message}
        </div>
      </td>
    </tr>
    
    ${additionalInfo ? `
    <tr>
      <td style="padding:0 40px 30px 40px;">
        <div style="background-color:#f8f9fa;border-left:4px solid #667eea;padding:20px;border-radius:0 8px 8px 0;">
          <div style="color:#495057;font-size:14px;line-height:1.5;">
            ${additionalInfo}
          </div>
        </div>
      </td>
    </tr>
    ` : ""}
    
    ${buttonUrl && buttonText ? `
    <tr>
      <td align="center" style="padding:0 40px 40px 40px;">
        <table role="presentation" cellpadding="0" cellspacing="0">
          <tr>
            <td style="border-radius:8px;background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);box-shadow:0 4px 15px rgba(102,126,234,0.4);">
              <a href="${buttonUrl}" 
                 style="background: transparent;
                        color:#ffffff;
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        font-weight:600;
                        font-size:16px;
                        text-decoration:none;
                        padding:16px 40px;
                        border-radius:8px;
                        display:inline-block;
                        letter-spacing:0.5px;
                        transition: all 0.3s ease;">
                ${buttonText}
              </a>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    ` : ""}
    
    <!-- Footer -->
    <tr>
      <td style="padding:30px 40px;background-color:#f8f9fa;border-top:1px solid #e9ecef;">
        <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
          <tr>
            <td style="text-align:center;">
              <p style="font-size:14px;color:#6c757d;margin:0 0 10px 0;font-weight:500;">
                Best regards,<br/>
                The ${BRAND_NAME} Team
              </p>
              <p style="font-size:12px;color:#8d99ae;margin:0;line-height:1.4;">
                This email was sent to you as part of your ${BRAND_NAME} account activity.<br/>
                If you have any questions, contact us at <a href="mailto:${SUPPORT_EMAIL}" style="color:#667eea;">${SUPPORT_EMAIL}</a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
    
  </table>
</td></tr>
</table>
</body>
</html>`;
}

async function sendEmail({ to, subject, title, message, buttonUrl, buttonText, additionalInfo }) {
  const html = createEmailTemplate({ title, message, buttonUrl, buttonText, additionalInfo });
  const text = `${title}\n\n${message.replace(/<[^>]*>/g, '')}\n\n${buttonUrl ? `Link: ${buttonUrl}` : ""}\n\nBest regards,\nThe ${BRAND_NAME} Team`;

  try {
    const info = await transporter.sendMail({
      from: `"${BRAND_NAME}" <${process.env.EMAIL_USER || '<EMAIL>'}>`,
      to,
      subject,
      text,
      html,
      replyTo: SUPPORT_EMAIL,
    });

    console.log('Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error: error.message };
  }
}
// Enhanced sendTestLinkEmail function for candidate assignment
async function sendTestLinkEmail(toEmail, testLink, testDetails = {}) {
  const {
    testName = "Assessment Test",
    duration = "60 minutes",
    instructions = "",
    expiryTime = "24 hours",
    candidateName = "",
    companyName = ""
  } = testDetails;

  const greeting = candidateName ? `Hello ${candidateName},` : "Hello,";
  const companyInfo = companyName ? ` from ${companyName}` : "";

  const message = `
    <p>${greeting}</p>
    <p>You have been invited to take the <strong>${testName}</strong>${companyInfo}. This is an important step in our evaluation process.</p>
    <p><strong>Test Details:</strong></p>
    <ul style="margin: 15px 0; padding-left: 20px;">
      <li>Duration: ${duration}</li>
      <li>Link expires in: ${expiryTime}</li>
      ${instructions ? `<li>Special instructions: ${instructions}</li>` : ''}
    </ul>
    <p>Please ensure you have a stable internet connection and are in a quiet environment before starting.</p>
  `;

  const additionalInfo = `
    <strong>Important Notes:</strong><br/>
    • Complete the test in one sitting - you cannot pause once started<br/>
    • Ensure your browser allows pop-ups and JavaScript<br/>
    • Contact support if you encounter any technical issues<br/>
    • This link is unique to you and should not be shared
  `;

  return sendEmail({
    to: toEmail,
    subject: `Test Assignment: ${testName} - Action Required`,
    title: `${testName} Assignment`,
    message,
    buttonUrl: testLink,
    buttonText: "Start Test Now",
    additionalInfo
  });
}

// New function for test creation notification to company
async function sendTestCreatedNotification(toEmail, testDetails = {}) {
  const {
    testName = "New Test",
    companyName = "",
    testId = "",
    totalQuestions = 0,
    duration = "60 minutes",
    scheduledDate = "",
    endDate = ""
  } = testDetails;

  const testUrl = `https://localhost:5173/testapplication/${testId}`;
  const companyInfo = companyName ? ` for ${companyName}` : "";

  const message = `
    <p>Hello,</p>
    <p>Your test <strong>"${testName}"</strong>${companyInfo} has been successfully created and is ready for assignment.</p>
    <p><strong>Test Summary:</strong></p>
    <ul style="margin: 15px 0; padding-left: 20px;">
      <li>Test Name: ${testName}</li>
      <li>Total Questions: ${totalQuestions}</li>
      <li>Duration: ${duration}</li>
      <li>Scheduled Date: ${scheduledDate}</li>
      <li>End Date: ${endDate}</li>
      <li>Test ID: ${testId}</li>
    </ul>
    <p>You can now assign candidates to this test through your dashboard.</p>
  `;

  const additionalInfo = `
    <strong>Next Steps:</strong><br/>
    • Review the test configuration in your dashboard<br/>
    • Assign candidates to the test<br/>
    • Monitor test progress and results<br/>
    • Test URL for candidates: ${testUrl}
  `;

  return sendEmail({
    to: toEmail,
    subject: `Test Created Successfully: ${testName}`,
    title: `Test "${testName}" Created`,
    message,
    buttonUrl: `https://localhost:5173/dashboard/tests/${testId}`,
    buttonText: "View Test Dashboard",
    additionalInfo
  });
}

// New function for bulk candidate assignment notification
async function sendBulkTestAssignmentNotification(assignments = []) {
  const results = [];

  for (const assignment of assignments) {
    try {
      const {
        candidateEmail,
        candidateName,
        testId,
        testName,
        duration,
        instructions,
        companyName,
        scheduledDate,
        endDate
      } = assignment;

      const testUrl = `https://localhost:5173/testapplication/${testId}`;

      // Calculate expiry time based on end date
      const expiryTime = endDate ? new Date(endDate).toLocaleDateString() : "24 hours";

      const result = await sendTestLinkEmail(candidateEmail, testUrl, {
        testName,
        duration: `${duration} minutes`,
        instructions,
        expiryTime,
        candidateName,
        companyName
      });

      results.push({
        candidateEmail,
        success: result.success,
        messageId: result.messageId,
        error: result.error
      });

      // Add small delay between emails to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      results.push({
        candidateEmail: assignment.candidateEmail,
        success: false,
        error: error.message
      });
    }
  }

  return results;
}

async function sendPasswordResetEmail(toEmail, resetLink) {
  return sendEmail({
    to: toEmail,
    subject: "Password Reset Request",
    title: "Password Reset Request",
    message:
      "We received a request to reset your password. Click the button below to set a new one. This link will expire in 1 hour.",
    buttonUrl: resetLink,
    buttonText: "Reset Password",
  });
}

async function sendWelcomeEmail(toEmail, userName) {
  return sendEmail({
    to: toEmail,
    subject: "Welcome to Resumeduilder!",
    title: `Welcome, ${userName}!`,
    message:
      "Thank you for joining Resumeduilder. We’re excited to have you on board. Get started by exploring the site and creating your first resume.",
    buttonUrl: "https://yourapp.com",
    buttonText: "Get Started",
  });
}


async function sendNotificationEmail(toEmail, subject, message) {
  return sendEmail({
    to: toEmail,
    subject,
    title: subject,
    message,
  });
}
async function sendOtpEmail(toEmail, otp) {
  const verificationLink = `http://localhost:5173/verify-otp?email=${encodeURIComponent(toEmail)}&otp=${otp}`;

  return sendEmail({
    to: toEmail,
    subject: "Email Verification Link",
    title: "Verify Your Email Address",
    message: `
      <p>Hello,</p>
      <p>Please verify your email by clicking the link below:</p>
      <p><a href="${verificationLink}" target="_blank" style="color: #2563eb; font-weight: bold;">Verify Email</a></p>
      <p>Or use the following OTP: <strong>${otp}</strong></p>
      <p>This link and OTP will expire in 10 minutes.</p>
    `,
  });
}
// async function sendTestLinkEmail(toEmail, testLink) {
//   return sendEmail({
//     to: toEmail,
//     subject: "Test Link",
//     title: "Test Link",
//     message: "Please click the button below to start the test.",
//     buttonUrl: testLink,
//     buttonText: "Start Test",
//   });
// }

module.exports = {
  sendPasswordResetEmail,
  sendWelcomeEmail,
  sendNotificationEmail,
  sendOtpEmail,
  sendTestLinkEmail,
  sendTestCreatedNotification,
  sendBulkTestAssignmentNotification
};
