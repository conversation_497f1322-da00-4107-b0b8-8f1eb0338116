const emailNotificationService = require('../services/emailNotificationService');
const { sendTestCreatedNotification, sendBulkTestAssignmentNotification } = require('../services/mailServices');

// Mock the mail services
jest.mock('../services/mailServices');
jest.mock('../config/logger', () => ({
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn()
}));

describe('EmailNotificationService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('sendTestCreationNotification', () => {
        const mockCompanyUser = {
            email: '<EMAIL>',
            name: 'Test Company'
        };

        const mockTestDetails = {
            testName: 'JavaScript Assessment',
            testId: '507f1f77bcf86cd799439011',
            totalQuestions: 10,
            duration: 60,
            scheduledDate: new Date('2024-01-15'),
            endDate: new Date('2024-01-20')
        };

        const mockCompany = {
            companyName: 'Tech Corp',
            name: 'Tech Corp'
        };

        it('should send test creation notification successfully', async () => {
            sendTestCreatedNotification.mockResolvedValue({
                success: true,
                messageId: 'test-message-id'
            });

            const result = await emailNotificationService.sendTestCreationNotification(
                mockCompanyUser,
                mockTestDetails,
                mockCompany
            );

            expect(result.success).toBe(true);
            expect(result.messageId).toBe('test-message-id');
            expect(sendTestCreatedNotification).toHaveBeenCalledWith(
                '<EMAIL>',
                expect.objectContaining({
                    testName: 'JavaScript Assessment',
                    companyName: 'Tech Corp',
                    testId: '507f1f77bcf86cd799439011',
                    totalQuestions: 10,
                    duration: '60 minutes'
                })
            );
        });

        it('should handle missing company email', async () => {
            const result = await emailNotificationService.sendTestCreationNotification(
                null,
                mockTestDetails,
                mockCompany
            );

            expect(result.success).toBe(false);
            expect(result.reason).toBe('NO_EMAIL');
            expect(sendTestCreatedNotification).not.toHaveBeenCalled();
        });

        it('should handle email sending failure', async () => {
            sendTestCreatedNotification.mockResolvedValue({
                success: false,
                error: 'SMTP connection failed'
            });

            const result = await emailNotificationService.sendTestCreationNotification(
                mockCompanyUser,
                mockTestDetails,
                mockCompany
            );

            expect(result.success).toBe(false);
            expect(result.reason).toBe('EMAIL_SEND_FAILED');
            expect(result.error).toBe('SMTP connection failed');
        });

        it('should handle unexpected errors', async () => {
            sendTestCreatedNotification.mockRejectedValue(new Error('Network error'));

            const result = await emailNotificationService.sendTestCreationNotification(
                mockCompanyUser,
                mockTestDetails,
                mockCompany
            );

            expect(result.success).toBe(false);
            expect(result.reason).toBe('UNEXPECTED_ERROR');
            expect(result.error).toBe('Network error');
        });
    });

    describe('sendCandidateAssignmentNotifications', () => {
        const mockCandidateIds = ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013'];
        
        const mockTest = {
            _id: '507f1f77bcf86cd799439011',
            testName: 'React Assessment',
            duration: 90,
            instructions: 'Please complete all questions',
            scheduledDate: new Date('2024-01-15'),
            endDate: new Date('2024-01-20')
        };

        const mockCompany = {
            companyName: 'Tech Solutions',
            name: 'Tech Solutions'
        };

        // Mock User model
        const mockUser = {
            find: jest.fn()
        };

        beforeEach(() => {
            // Mock the User model
            jest.doMock('../models/User', () => mockUser);
        });

        it('should send notifications to all candidates successfully', async () => {
            const mockCandidates = [
                { _id: '507f1f77bcf86cd799439012', email: '<EMAIL>', name: 'John Doe' },
                { _id: '507f1f77bcf86cd799439013', email: '<EMAIL>', name: 'Jane Smith' }
            ];

            mockUser.find.mockResolvedValue(mockCandidates);
            
            sendBulkTestAssignmentNotification.mockResolvedValue([
                { candidateEmail: '<EMAIL>', success: true, messageId: 'msg1' },
                { candidateEmail: '<EMAIL>', success: true, messageId: 'msg2' }
            ]);

            const result = await emailNotificationService.sendCandidateAssignmentNotifications(
                mockCandidateIds,
                mockTest,
                mockCompany
            );

            expect(result.success).toBe(true);
            expect(result.totalCandidates).toBe(2);
            expect(result.successfulEmails).toBe(2);
            expect(result.failedEmails).toBe(0);
        });

        it('should handle partial email failures', async () => {
            const mockCandidates = [
                { _id: '507f1f77bcf86cd799439012', email: '<EMAIL>', name: 'John Doe' },
                { _id: '507f1f77bcf86cd799439013', email: '<EMAIL>', name: 'Jane Smith' }
            ];

            mockUser.find.mockResolvedValue(mockCandidates);
            
            sendBulkTestAssignmentNotification.mockResolvedValue([
                { candidateEmail: '<EMAIL>', success: true, messageId: 'msg1' },
                { candidateEmail: '<EMAIL>', success: false, error: 'Invalid email' }
            ]);

            const result = await emailNotificationService.sendCandidateAssignmentNotifications(
                mockCandidateIds,
                mockTest,
                mockCompany
            );

            expect(result.success).toBe(true); // Still success if at least one email sent
            expect(result.totalCandidates).toBe(2);
            expect(result.successfulEmails).toBe(1);
            expect(result.failedEmails).toBe(1);
        });

        it('should handle empty candidate list', async () => {
            const result = await emailNotificationService.sendCandidateAssignmentNotifications(
                [],
                mockTest,
                mockCompany
            );

            expect(result.success).toBe(true);
            expect(result.totalCandidates).toBe(0);
            expect(result.successfulEmails).toBe(0);
            expect(result.failedEmails).toBe(0);
        });

        it('should handle no valid candidates found', async () => {
            mockUser.find.mockResolvedValue([]);

            const result = await emailNotificationService.sendCandidateAssignmentNotifications(
                mockCandidateIds,
                mockTest,
                mockCompany
            );

            expect(result.success).toBe(false);
            expect(result.reason).toBe('NO_VALID_CANDIDATES');
            expect(result.totalCandidates).toBe(2);
            expect(result.successfulEmails).toBe(0);
            expect(result.failedEmails).toBe(2);
        });
    });

    describe('validateEmailConfiguration', () => {
        it('should validate email configuration successfully', async () => {
            // Mock nodemailer
            const mockTransporter = {
                verify: jest.fn().mockResolvedValue(true)
            };
            
            const mockNodemailer = {
                createTransporter: jest.fn().mockReturnValue(mockTransporter)
            };

            jest.doMock('nodemailer', () => mockNodemailer);

            const result = await emailNotificationService.validateEmailConfiguration();

            expect(result.success).toBe(true);
            expect(result.message).toBe('Email configuration is valid');
        });

        it('should handle email configuration validation failure', async () => {
            // Mock nodemailer with failure
            const mockTransporter = {
                verify: jest.fn().mockRejectedValue(new Error('Authentication failed'))
            };
            
            const mockNodemailer = {
                createTransporter: jest.fn().mockReturnValue(mockTransporter)
            };

            jest.doMock('nodemailer', () => mockNodemailer);

            const result = await emailNotificationService.validateEmailConfiguration();

            expect(result.success).toBe(false);
            expect(result.error).toBe('Authentication failed');
        });
    });
});
