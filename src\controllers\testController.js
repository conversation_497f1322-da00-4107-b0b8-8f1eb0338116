const Test = require('../models/Test');
const Job = require('../models/Job');
const Question = require('../models/Question');
const QuestionBundle = require('../models/QuestionBundle');
const Company = require('../models/Company');
const User = require('../models/User');
const mongoose = require('mongoose');
const emailNotificationService = require('../services/emailNotificationService');

// 1. CREATE TEST
exports.createTest = async (req, res, next) => {
    try {
        const { testName, description, duration, passingScore, scheduledDate, endDate, questions, associatedJobs = [], instructions, allowedAttempts, randomizeQuestions, showResults } = req.body;
        if (!testName || !duration || !scheduledDate || !endDate || !questions || questions.length === 0) {
            return res.status(400).json({ error: 'Missing fields: test name, schedule, or questions.' });
        }

        // Get company profile for proper company ID
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found. Please create a company profile first.' });
        }

        const totalPoints = questions.reduce((sum, q) => sum + (q.points || 1), 0);

        const test = await Test.create({
            testName,
            companyId: company._id, // Use company._id instead of req.user.id
            associatedJobs,
            description,
            duration,
            passingScore,
            scheduledDate,
            endDate,
            questions,
            totalPoints,
            instructions,
            allowedAttempts,
            randomizeQuestions,
            showResults
        });

        // Optionally assign testId to jobs
        if (associatedJobs.length > 0) {
            await Job.updateMany(
                { _id: { $in: associatedJobs }, companyId: company._id }, // Add company check for security
                { hasTest: true, testId: test._id }
            );
        }

        // Send test creation notification email to company
        const companyUser = await User.findById(req.user.id);
        const emailResult = await emailNotificationService.sendTestCreationNotification(
            companyUser,
            {
                testName,
                testId: test._id,
                totalQuestions: questions.length,
                duration,
                scheduledDate,
                endDate
            },
            company
        );

        res.status(201).json({ success: true, message: 'Test created successfully.', test });
    } catch (err) {
        next(err);
    }
};

// 2. GET ALL TESTS (FOR COMPANY)
exports.getTests = async (req, res, next) => {
    try {
        let companyFilter;

        if (req.user.role === 'admin') {
            // Admin can see all tests
            companyFilter = {};
        } else {
            // For company users, only show tests that belong to their company
            const company = await Company.findOne({ userId: req.user.id });
            if (!company) {
                return res.status(404).json({ error: 'Company profile not found. Please create a company profile first.' });
            }

            // Only show tests that belong to this specific company
            companyFilter = { companyId: company._id };
        }

        const tests = await Test.find(companyFilter).sort({ createdAt: -1 });
        res.json({ success: true, count: tests.length, tests });
    } catch (err) {
        next(err);
    }
};

// 3. GET TEST BY ID
exports.getTestDetails = async (req, res, next) => {
    try {
        const test = await Test.findById(req.params.testId)
            .populate('questions.questionId', 'questionText questionType options');

        if (!test) return res.status(404).json({ error: 'Test not found' });

        res.json({ success: true, test });
    } catch (err) {
        next(err);
    }
};

// 4. UPDATE TEST
exports.updateTest = async (req, res, next) => {
    try {
        const updated = await Test.findByIdAndUpdate(req.params.testId, { ...req.body }, { new: true });
        if (!updated) return res.status(404).json({ error: 'Test not found' });
        res.json({ success: true, message: 'Test updated successfully', test: updated });
    } catch (err) {
        next(err);
    }
};

// 5. DELETE TEST
exports.deleteTest = async (req, res, next) => {
    try {
        const test = await Test.findByIdAndDelete(req.params.testId);
        if (!test) return res.status(404).json({ error: 'Test not found' });

        // Remove testId reference from jobs
        await Job.updateMany({ testId: test._id }, { hasTest: false, testId: null });

        res.json({ success: true, message: 'Test deleted successfully' });
    } catch (err) {
        next(err);
    }
};

// 6. ASSIGN CANDIDATES TO TEST (ENHANCED WITH JOB APPLICATION VERIFICATION)
exports.assignCandidatesToTest = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const { candidateIds = [] } = req.body;

        if (!candidateIds || candidateIds.length === 0) {
            return res.status(400).json({ error: 'Candidate IDs are required' });
        }

        // Get company info for security
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const test = await Test.findOne({
            _id: testId,
            companyId: company._id
        }).populate('associatedJobs');

        if (!test) {
            return res.status(404).json({ error: 'Test not found or access denied' });
        }

        // Enhanced security: If test has associated jobs, verify candidates applied for them
        if (test.associatedJobs && test.associatedJobs.length > 0) {
            const jobIds = test.associatedJobs.map(job => job._id);

            // Get candidates who applied for associated jobs
            const validApplicants = await Job.aggregate([
                {
                    $match: {
                        _id: { $in: jobIds },
                        'applicants.candidateId': {
                            $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id))
                        }
                    }
                },
                { $unwind: '$applicants' },
                {
                    $match: {
                        'applicants.candidateId': {
                            $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id))
                        }
                    }
                },
                {
                    $group: {
                        _id: null,
                        validCandidates: { $addToSet: '$applicants.candidateId' }
                    }
                }
            ]);

            const validCandidateIds = validApplicants.length > 0 ?
                validApplicants[0].validCandidates.map(id => id.toString()) : [];

            const invalidCandidates = candidateIds.filter(id => !validCandidateIds.includes(id));

            if (invalidCandidates.length > 0) {
                return res.status(400).json({
                    error: 'Some candidates have not applied for the required jobs',
                    invalidCandidates,
                    requiredJobs: test.associatedJobs.map(job => ({
                        id: job._id,
                        title: job.title
                    })),
                    message: 'Only candidates who have applied for the associated jobs can be assigned to this test'
                });
            }
        }

        // Assign candidates (avoid duplicates)
        let assignedCount = 0;
        const newlyAssignedIds = [];
        candidateIds.forEach(id => {
            if (!test.participants.some(p => p.candidateId.toString() === id)) {
                test.participants.push({ candidateId: id, status: 'assigned' });
                assignedCount++;
                newlyAssignedIds.push(id);
            }
        });

        await test.save();

        // Send email notifications to newly assigned candidates
        const emailResult = await emailNotificationService.sendCandidateAssignmentNotifications(
            newlyAssignedIds,
            test,
            company
        );

        res.json({
            success: true,
            message: `${assignedCount} candidates assigned successfully.`,
            totalParticipants: test.participants.length,
            assignedCandidates: assignedCount
        });
    } catch (err) {
        next(err);
    }
};

// 7. START TEST
exports.startTest = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const userId = req.user.id;

        const test = await Test.findById(testId);
        if (!test) return res.status(404).json({ error: 'Test not found' });

        const now = new Date();
        if (now < test.scheduledDate || now > test.endDate) {
            return res.status(403).json({ error: 'Test is not currently available' });
        }

        const participant = test.participants.find(p => p.candidateId.toString() === userId);
        if (!participant) return res.status(403).json({ error: 'You are not assigned to this test' });

        if (participant.startedAt) return res.status(400).json({ error: 'Test already started' });

        participant.startedAt = new Date();
        participant.status = 'started';

        await test.save();

        let questions = await Question.find({ _id: { $in: test.questions.map(q => q.questionId) } });
        if (test.randomizeQuestions) questions = questions.sort(() => Math.random() - 0.5);

        res.json({
            success: true,
            test: {
                testId: test._id,
                duration: test.duration,
                testName: test.testName
            },
            questions
        });
    } catch (err) {
        next(err);
    }
};

// 8. SUBMIT TEST
exports.submitTest = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const userId = req.user.id;
        const { answers = [] } = req.body;

        const test = await Test.findById(testId).populate('questions.questionId');
        if (!test) return res.status(404).json({ error: 'Test not found' });

        const participant = test.participants.find(p => p.candidateId.toString() === userId);
        if (!participant || participant.status !== 'started') return res.status(400).json({ error: 'Not authorized or already submitted' });

        let score = 0;
        let total = 0;

        const answerMap = new Map();
        test.questions.forEach(q => {
            answerMap.set(q.questionId._id.toString(), {
                points: q.points,
                question: q.questionId
            });
            total += q.points;
        });

        answers.forEach(a => {
            const data = answerMap.get(a.questionId);
            const question = data?.question;

            let isCorrect = false;
            if (question?.questionType === 'MCQ') {
                const correctOption = question.options.find(o => o.isCorrect);
                isCorrect = correctOption && correctOption.text === a.answer;
            }

            if (isCorrect) score += data.points;

            participant.answers.push({
                questionId: a.questionId,
                answer: a.answer,
                isCorrect,
                pointsEarned: isCorrect ? data.points : 0
            });
        });

        participant.totalScore = total;
        participant.score = score;
        participant.percentage = Math.round((score / total) * 100);
        participant.status = 'submitted';
        participant.submittedAt = new Date();

        await test.save();

        res.json({ success: true, message: 'Test submitted', score, percentage: participant.percentage });
    } catch (err) {
        next(err);
    }
};

// 9. GET RESULTS (company)
exports.getTestResults = async (req, res, next) => {
    try {
        const test = await Test.findById(req.params.testId).populate('participants.candidateId', 'name email');
        if (!test) return res.status(404).json({ error: 'Test not found' });

        res.json({ success: true, results: test.participants });
    } catch (err) {
        next(err);
    }
};

// 10. ANALYTICS
exports.getTestAnalytics = async (req, res, next) => {
    try {
        const test = await Test.findById(req.params.testId);
        const data = {
            totalAssigned: test.participants.length,
            started: test.participants.filter(p => p.startedAt).length,
            submitted: test.participants.filter(p => p.status === 'submitted').length,
            avgScore:
                test.participants.reduce((acc, p) => acc + (p.score || 0), 0) / (test.participants.length || 1)
        };
        res.json({ success: true, analytics: data });
    } catch (err) {
        next(err);
    }
};

// 11. MANUAL FEEDBACK & GRADING
exports.updateParticipantFeedback = async (req, res, next) => {
    const { feedback } = req.body;
    const test = await Test.findById(req.params.testId);
    const participant = test.participants.find(p => p.candidateId.toString() === req.params.participantId);
    if (!participant) return res.status(404).json({ error: 'Participant not found' });
    participant.feedback = feedback;
    if (participant.status !== 'evaluated') participant.status = 'evaluated';
    await test.save();
    res.json({ success: true, message: 'Feedback updated' });
};

exports.manualGradeParticipant = async (req, res, next) => {
    const { score, feedback } = req.body;
    const test = await Test.findById(req.params.testId);
    const participant = test.participants.find(p => p.candidateId.toString() === req.params.participantId);
    if (!participant) return res.status(404).json({ error: 'Participant not found' });
    participant.score = score;
    participant.feedback = feedback;
    participant.status = 'evaluated';
    await test.save();
    res.json({ success: true, message: 'Participant manually graded' });
};

// 12. CANDIDATE - GET ASSIGNED TESTS
exports.getCandidateTests = async (req, res, next) => {
    const tests = await Test.find({ 'participants.candidateId': req.user.id }).sort({ scheduledDate: -1 });
    res.json({ success: true, count: tests.length, tests });
};

// 13. CANDIDATE - GET PERSONAL RESULT
exports.getCandidateResult = async (req, res, next) => {
    const test = await Test.findById(req.params.testId);
    const p = test.participants.find(p => p.candidateId.toString() === req.user.id);
    if (!p) return res.status(403).json({ error: 'You did not take this test' });
    if (!test.showResults) return res.status(403).json({ error: 'Results are not visible for this test' });
    res.json({ success: true, result: p });
};

// ============ ENHANCED QUESTION MANAGEMENT ============

// Get questions by category with security checks
exports.getQuestionsByCategory = async (req, res, next) => {
    try {
        const { category } = req.query;

        if (!category) {
            return res.status(400).json({ error: 'Category parameter is required' });
        }

        // Get company info for security
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const questions = await Question.find({
            companyId: company._id,
            category: category,
            isActive: true
        })
        .select('questionText questionType category difficulty points')
        .sort({ createdAt: -1 });

        res.json({
            success: true,
            category,
            count: questions.length,
            questions
        });
    } catch (err) {
        next(err);
    }
};

// Get all question categories for the company
exports.getQuestionCategories = async (req, res, next) => {
    try {
        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const categories = await Question.distinct('category', {
            companyId: company._id,
            isActive: true
        });

        // Get count for each category
        const categoriesWithCount = await Promise.all(
            categories.map(async (category) => {
                const count = await Question.countDocuments({
                    companyId: company._id,
                    category,
                    isActive: true
                });
                return { category, count };
            })
        );

        res.json({
            success: true,
            categories: categoriesWithCount
        });
    } catch (err) {
        next(err);
    }
};

// Advanced question filtering
exports.filterQuestions = async (req, res, next) => {
    try {
        const {
            searchTerm,
            category,
            difficulty,
            type,
            page = 1,
            limit = 20
        } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build filter object
        const filter = {
            companyId: company._id,
            isActive: true
        };

        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (type) filter.questionType = type;

        if (searchTerm) {
            filter.$or = [
                { questionText: { $regex: searchTerm, $options: 'i' } },
                { explanation: { $regex: searchTerm, $options: 'i' } }
            ];
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const questions = await Question.find(filter)
            .select('questionText questionType category difficulty points explanation')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await Question.countDocuments(filter);

        res.json({
            success: true,
            questions,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total,
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        next(err);
    }
};

// Get questions for a specific test
exports.getTestQuestions = async (req, res, next) => {
    try {
        const { testId } = req.params;

        const test = await Test.findById(testId)
            .populate('questions.questionId', 'questionText questionType options correctAnswer difficulty category')
            .select('questions testName');

        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        res.json({
            success: true,
            testName: test.testName,
            questions: test.questions
        });
    } catch (err) {
        next(err);
    }
};

// Add questions to existing test
exports.addQuestionsToTest = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const { questionIds, points } = req.body;

        if (!questionIds || !Array.isArray(questionIds) || questionIds.length === 0) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const test = await Test.findById(testId);
        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // Security check - ensure test belongs to company
        const company = await Company.findOne({ userId: req.user.id });
        if (!company || test.companyId.toString() !== company._id.toString()) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Verify questions belong to the company
        const questions = await Question.find({
            _id: { $in: questionIds },
            companyId: company._id,
            isActive: true
        });

        if (questions.length !== questionIds.length) {
            return res.status(400).json({ error: 'Some questions not found or not accessible' });
        }

        // Add questions to test (avoid duplicates)
        const existingQuestionIds = test.questions.map(q => q.questionId.toString());
        const newQuestions = questionIds
            .filter(qId => !existingQuestionIds.includes(qId))
            .map(qId => ({
                questionId: qId,
                points: points || 1
            }));

        test.questions.push(...newQuestions);
        await test.save();

        res.json({
            success: true,
            message: `${newQuestions.length} questions added to test`,
            totalQuestions: test.questions.length
        });
    } catch (err) {
        next(err);
    }
};

// Remove questions from test
exports.removeQuestionsFromTest = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const { questionIds } = req.body;

        if (!questionIds || !Array.isArray(questionIds)) {
            return res.status(400).json({ error: 'Question IDs array is required' });
        }

        const test = await Test.findById(testId);
        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // Security check
        const company = await Company.findOne({ userId: req.user.id });
        if (!company || test.companyId.toString() !== company._id.toString()) {
            return res.status(403).json({ error: 'Access denied' });
        }

        // Remove questions
        const initialCount = test.questions.length;
        test.questions = test.questions.filter(
            q => !questionIds.includes(q.questionId.toString())
        );

        await test.save();

        res.json({
            success: true,
            message: `${initialCount - test.questions.length} questions removed`,
            totalQuestions: test.questions.length
        });
    } catch (err) {
        next(err);
    }
};

// ============ QUESTION BUNDLE MANAGEMENT ============

// Create question bundle
exports.createQuestionBundle = async (req, res, next) => {
    try {
        const {
            bundleName,
            description,
            category,
            difficulty,
            questionIds,
            tags
        } = req.body;

        if (!bundleName || !category || !questionIds || !Array.isArray(questionIds)) {
            return res.status(400).json({
                error: 'Bundle name, category, and question IDs are required'
            });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Verify questions belong to company
        const questions = await Question.find({
            _id: { $in: questionIds },
            companyId: company._id,
            isActive: true
        });

        if (questions.length !== questionIds.length) {
            return res.status(400).json({
                error: 'Some questions not found or not accessible'
            });
        }

        // Create bundle
        const bundle = new QuestionBundle({
            companyId: company._id,
            createdBy: req.user.id,
            bundleName,
            description,
            category,
            difficulty: difficulty || 'Mixed',
            questions: questionIds.map((qId, index) => ({
                questionId: qId,
                points: 1, // Default points
                order: index
            })),
            tags: tags || []
        });

        await bundle.save();

        res.status(201).json({
            success: true,
            message: 'Question bundle created successfully',
            bundle
        });
    } catch (err) {
        next(err);
    }
};

// Get all question bundles
exports.getQuestionBundles = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 10,
            category,
            difficulty,
            search
        } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const filter = {
            companyId: company._id,
            isActive: true
        };

        if (category) filter.category = category;
        if (difficulty) filter.difficulty = difficulty;
        if (search) {
            filter.$or = [
                { bundleName: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
                { tags: { $in: [new RegExp(search, 'i')] } }
            ];
        }

        const skip = (parseInt(page) - 1) * parseInt(limit);

        const bundles = await QuestionBundle.find(filter)
            .select('bundleName description category difficulty totalQuestions totalPoints estimatedDuration usageCount lastUsedAt createdAt')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        const total = await QuestionBundle.countDocuments(filter);

        res.json({
            success: true,
            bundles,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(total / limit),
                total,
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        next(err);
    }
};

// Get specific question bundle
exports.getQuestionBundle = async (req, res, next) => {
    try {
        const { bundleId } = req.params;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const bundle = await QuestionBundle.findOne({
            _id: bundleId,
            companyId: company._id
        }).populate('questions.questionId', 'questionText questionType category difficulty options correctAnswer');

        if (!bundle) {
            return res.status(404).json({ error: 'Question bundle not found' });
        }

        res.json({
            success: true,
            bundle
        });
    } catch (err) {
        next(err);
    }
};

// Update question bundle
exports.updateQuestionBundle = async (req, res, next) => {
    try {
        const { bundleId } = req.params;
        const {
            bundleName,
            description,
            category,
            difficulty,
            questionIds,
            tags
        } = req.body;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const bundle = await QuestionBundle.findOne({
            _id: bundleId,
            companyId: company._id
        });

        if (!bundle) {
            return res.status(404).json({ error: 'Question bundle not found' });
        }

        // Update fields
        if (bundleName) bundle.bundleName = bundleName;
        if (description !== undefined) bundle.description = description;
        if (category) bundle.category = category;
        if (difficulty) bundle.difficulty = difficulty;
        if (tags) bundle.tags = tags;

        // Update questions if provided
        if (questionIds && Array.isArray(questionIds)) {
            // Verify questions belong to company
            const questions = await Question.find({
                _id: { $in: questionIds },
                companyId: company._id,
                isActive: true
            });

            if (questions.length !== questionIds.length) {
                return res.status(400).json({
                    error: 'Some questions not found or not accessible'
                });
            }

            bundle.questions = questionIds.map((qId, index) => ({
                questionId: qId,
                points: 1,
                order: index
            }));
        }

        bundle.updatedBy = req.user.id;
        await bundle.save();

        res.json({
            success: true,
            message: 'Question bundle updated successfully',
            bundle
        });
    } catch (err) {
        next(err);
    }
};

// Delete question bundle
exports.deleteQuestionBundle = async (req, res, next) => {
    try {
        const { bundleId } = req.params;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const bundle = await QuestionBundle.findOneAndDelete({
            _id: bundleId,
            companyId: company._id
        });

        if (!bundle) {
            return res.status(404).json({ error: 'Question bundle not found' });
        }

        res.json({
            success: true,
            message: 'Question bundle deleted successfully'
        });
    } catch (err) {
        next(err);
    }
};

// ============ ENHANCED CANDIDATE MANAGEMENT ============

// Get candidates who applied for jobs (SECURITY ENHANCED)
exports.getCandidates = async (req, res, next) => {
    try {
        const {
            page = 1,
            limit = 10,
            jobId,
            status = 'all',
            sortBy = 'appliedAt',
            sortOrder = 'desc'
        } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build aggregation pipeline to get only candidates who applied for company jobs
        const pipeline = [
            // Match jobs belonging to this company
            {
                $match: {
                    companyId: company._id,
                    isActive: true
                }
            }
        ];

        // Filter by specific job if provided
        if (jobId) {
            if (!mongoose.Types.ObjectId.isValid(jobId)) {
                return res.status(400).json({ error: 'Invalid job ID' });
            }
            pipeline[0].$match._id = mongoose.Types.ObjectId.createFromHexString(jobId);
        }

        // Add stages to get applicants
        pipeline.push(
            // Unwind applicants array
            { $unwind: '$applicants' },
            // Filter by status if specified
            ...(status !== 'all' ? [{ $match: { 'applicants.status': status } }] : []),
            // Lookup candidate details
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateInfo'
                }
            },
            { $unwind: '$candidateInfo' },
            // Project required fields
            {
                $project: {
                    candidateId: '$applicants.candidateId',
                    name: '$candidateInfo.name',
                    email: '$candidateInfo.email',
                    appliedAt: '$applicants.appliedAt',
                    status: '$applicants.status',
                    testScore: '$applicants.testScore',
                    jobTitle: '$title',
                    jobId: '$_id'
                }
            }
        );

        // Add sorting
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;
        pipeline.push({ $sort: sortOptions });

        // Execute aggregation
        const candidates = await Job.aggregate(pipeline);

        // Apply pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const paginatedCandidates = candidates.slice(skip, skip + parseInt(limit));

        res.json({
            success: true,
            candidates: paginatedCandidates,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(candidates.length / limit),
                total: candidates.length,
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        next(err);
    }
};

// Enhanced candidate search with job application filtering
exports.searchCandidates = async (req, res, next) => {
    try {
        const {
            search,
            experience,
            skills,
            location,
            status = 'all',
            jobId,
            page = 1,
            limit = 10
        } = req.query;

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Build aggregation pipeline
        const pipeline = [
            // Match jobs belonging to this company
            {
                $match: {
                    companyId: company._id,
                    isActive: true,
                    ...(jobId && mongoose.Types.ObjectId.isValid(jobId) ?
                        { _id: mongoose.Types.ObjectId.createFromHexString(jobId) } : {})
                }
            },
            // Unwind applicants
            { $unwind: '$applicants' },
            // Filter by status
            ...(status !== 'all' ? [{ $match: { 'applicants.status': status } }] : []),
            // Lookup candidate details with profile
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateInfo'
                }
            },
            { $unwind: '$candidateInfo' }
        ];

        // Add search filters
        const searchFilters = [];

        if (search) {
            searchFilters.push({
                $or: [
                    { 'candidateInfo.name': { $regex: search, $options: 'i' } },
                    { 'candidateInfo.email': { $regex: search, $options: 'i' } }
                ]
            });
        }

        if (location) {
            searchFilters.push({
                'candidateInfo.profile.location': { $regex: location, $options: 'i' }
            });
        }

        if (searchFilters.length > 0) {
            pipeline.push({ $match: { $and: searchFilters } });
        }

        // Project final fields
        pipeline.push({
            $project: {
                candidateId: '$applicants.candidateId',
                name: '$candidateInfo.name',
                email: '$candidateInfo.email',
                appliedAt: '$applicants.appliedAt',
                status: '$applicants.status',
                testScore: '$applicants.testScore',
                jobTitle: '$title',
                jobId: '$_id',
                profile: '$candidateInfo.profile'
            }
        });

        // Execute aggregation
        const candidates = await Job.aggregate(pipeline);

        // Apply pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const paginatedCandidates = candidates.slice(skip, skip + parseInt(limit));

        res.json({
            success: true,
            candidates: paginatedCandidates,
            pagination: {
                current: parseInt(page),
                pages: Math.ceil(candidates.length / limit),
                total: candidates.length,
                limit: parseInt(limit)
            }
        });
    } catch (err) {
        next(err);
    }
};

// Get candidates available for specific test (job-based filtering)
exports.getAvailableCandidatesForTest = async (req, res, next) => {
    try {
        const { testId } = req.query;

        if (!testId) {
            return res.status(400).json({ error: 'Test ID is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        // Get test details
        const test = await Test.findOne({
            _id: testId,
            companyId: company._id
        }).populate('associatedJobs');

        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // Get candidates who applied for associated jobs
        const jobIds = test.associatedJobs.map(job => job._id);

        const pipeline = [
            {
                $match: {
                    _id: { $in: jobIds },
                    companyId: company._id
                }
            },
            { $unwind: '$applicants' },
            {
                $match: {
                    'applicants.status': { $in: ['applied', 'test_pending', 'shortlisted'] }
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'applicants.candidateId',
                    foreignField: '_id',
                    as: 'candidateInfo'
                }
            },
            { $unwind: '$candidateInfo' },
            {
                $project: {
                    candidateId: '$applicants.candidateId',
                    name: '$candidateInfo.name',
                    email: '$candidateInfo.email',
                    appliedAt: '$applicants.appliedAt',
                    status: '$applicants.status',
                    jobTitle: '$title',
                    jobId: '$_id'
                }
            }
        ];

        const availableCandidates = await Job.aggregate(pipeline);

        // Remove candidates already assigned to this test
        const assignedCandidateIds = test.participants.map(p => p.candidateId.toString());
        const filteredCandidates = availableCandidates.filter(
            candidate => !assignedCandidateIds.includes(candidate.candidateId.toString())
        );

        res.json({
            success: true,
            candidates: filteredCandidates,
            testName: test.testName,
            associatedJobs: test.associatedJobs.map(job => ({
                id: job._id,
                title: job.title
            }))
        });
    } catch (err) {
        next(err);
    }
};

// Direct candidate assignment to test (enhanced security)
exports.assignCandidatesDirectly = async (req, res, next) => {
    try {
        const { testId } = req.params;
        const { candidateIds } = req.body;

        if (!candidateIds || !Array.isArray(candidateIds)) {
            return res.status(400).json({ error: 'Candidate IDs array is required' });
        }

        const company = await Company.findOne({ userId: req.user.id });
        if (!company) {
            return res.status(404).json({ error: 'Company profile not found' });
        }

        const test = await Test.findOne({
            _id: testId,
            companyId: company._id
        }).populate('associatedJobs');

        if (!test) {
            return res.status(404).json({ error: 'Test not found' });
        }

        // Verify candidates have applied for associated jobs
        const jobIds = test.associatedJobs.map(job => job._id);
        const validCandidates = await Job.aggregate([
            {
                $match: {
                    _id: { $in: jobIds },
                    'applicants.candidateId': { $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id)) }
                }
            },
            { $unwind: '$applicants' },
            {
                $match: {
                    'applicants.candidateId': { $in: candidateIds.map(id => mongoose.Types.ObjectId.createFromHexString(id)) }
                }
            },
            {
                $group: {
                    _id: null,
                    candidateIds: { $addToSet: '$applicants.candidateId' }
                }
            }
        ]);

        const validCandidateIds = validCandidates.length > 0 ?
            validCandidates[0].candidateIds.map(id => id.toString()) : [];

        const invalidCandidates = candidateIds.filter(id => !validCandidateIds.includes(id));

        if (invalidCandidates.length > 0) {
            return res.status(400).json({
                error: 'Some candidates have not applied for the associated jobs',
                invalidCandidates
            });
        }

        // Add candidates to test (avoid duplicates)
        const existingCandidateIds = test.participants.map(p => p.candidateId.toString());
        const newCandidateIds = candidateIds.filter(id => !existingCandidateIds.includes(id));
        const newCandidates = newCandidateIds.map(id => ({
            candidateId: id,
            status: 'assigned'
        }));

        test.participants.push(...newCandidates);
        await test.save();

        // Send email notifications to newly assigned candidates
        const emailResult = await emailNotificationService.sendCandidateAssignmentNotifications(
            newCandidateIds,
            test,
            company
        );

        res.json({
            success: true,
            message: `${newCandidates.length} candidates assigned to test`,
            totalParticipants: test.participants.length
        });
    } catch (err) {
        next(err);
    }
};

// Email configuration validation endpoint
exports.validateEmailConfiguration = async (req, res, next) => {
    try {
        const result = await emailNotificationService.validateEmailConfiguration();

        if (result.success) {
            res.json({
                success: true,
                message: 'Email configuration is valid and working',
                details: result
            });
        } else {
            res.status(500).json({
                success: false,
                message: 'Email configuration validation failed',
                error: result.error,
                details: result
            });
        }
    } catch (err) {
        next(err);
    }
};
