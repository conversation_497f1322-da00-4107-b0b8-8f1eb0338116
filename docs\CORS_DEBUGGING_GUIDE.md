# CORS Error Debugging Guide

## Changes Made

### 1. Updated CORS Configuration in app.js
- Added multiple origin variations (with/without trailing slash)
- Added localhost development support
- Added environment-based configuration
- Added CORS debugging logs

### 2. Added Environment Variables
```env
ALLOWED_ORIGINS=http://localhost:5173,http://localhost:3000,http://localhost:3001,https://prouj.vercel.app,https://prouj.vercel.app/,https://resume-builder-lilac-eta.vercel.app
```

## Debugging Steps

### 1. Check Current Origin
Add this to your frontend console to see what origin is being sent:
```javascript
console.log('Current origin:', window.location.origin);
```

### 2. Check Network Tab
1. Open browser DevTools (F12)
2. Go to Network tab
3. Make a request to your API
4. Look for the request headers:
   - `Origin: https://prouj.vercel.app`
   - Check if there's a trailing slash or not

### 3. Check Server Logs
The updated CORS configuration now logs blocked origins:
```
CORS blocked origin: https://some-blocked-origin.com
```

### 4. Test CORS Manually
Use curl to test CORS:
```bash
# Test with your Vercel URL
curl -H "Origin: https://prouj.vercel.app" \
     -H "Access-Control-Request-Method: GET" \
     -H "Access-Control-Request-Headers: X-Requested-With" \
     -X OPTIONS \
     http://localhost:5000/api/auth/me

# Should return CORS headers if allowed
```

## Common CORS Issues & Solutions

### 1. Trailing Slash Mismatch
**Problem**: `https://prouj.vercel.app` vs `https://prouj.vercel.app/`
**Solution**: Added both variations to allowed origins

### 2. Subdomain Issues
**Problem**: Request from `www.prouj.vercel.app` but only `prouj.vercel.app` is allowed
**Solution**: Add all subdomain variations

### 3. Development vs Production URLs
**Problem**: Different URLs in development and production
**Solution**: Use environment variables for configuration

### 4. Browser Cache
**Problem**: Browser caching old CORS policy
**Solution**: Hard refresh (Ctrl+Shift+R) or clear browser cache

## Quick Fixes

### Option 1: Temporary - Allow All Origins (Development Only)
```javascript
// In app.js - ONLY for debugging
app.use(cors({
    origin: true, // Allow all origins
    credentials: true
}));
```

### Option 2: Wildcard for Vercel Subdomains
```javascript
app.use(cors({
    origin: function (origin, callback) {
        if (!origin) return callback(null, true);
        
        // Allow all Vercel app subdomains
        if (origin.includes('.vercel.app')) {
            return callback(null, true);
        }
        
        // ... rest of logic
    },
    credentials: true
}));
```

### Option 3: Environment-Specific Configuration
```javascript
const corsOptions = {
    origin: process.env.NODE_ENV === 'development' 
        ? true  // Allow all in development
        : allowedOrigins,
    credentials: true
};

app.use(cors(corsOptions));
```

## Verification Steps

### 1. Restart Server
After making changes:
```bash
npm run dev
```

### 2. Check Response Headers
Successful CORS should include these headers:
```
Access-Control-Allow-Origin: https://prouj.vercel.app
Access-Control-Allow-Credentials: true
```

### 3. Test Different Endpoints
Test multiple endpoints to ensure CORS works across your API:
```javascript
// Test these endpoints from your frontend
fetch('/api/auth/me')
fetch('/api/admin/users')
fetch('/api/company/profile')
```

## Frontend Debugging

### 1. Check Request Origin
```javascript
// In your frontend code
console.log('Making request from:', window.location.origin);
```

### 2. Handle CORS Errors
```javascript
fetch('/api/auth/me')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .catch(error => {
        if (error.message.includes('CORS')) {
            console.error('CORS Error - check allowed origins on server');
        }
        console.error('Request failed:', error);
    });
```

### 3. Axios Configuration
If using Axios, ensure proper configuration:
```javascript
const api = axios.create({
    baseURL: 'http://localhost:5000/api',
    withCredentials: true,
    headers: {
        'Content-Type': 'application/json'
    }
});

// Add request interceptor to log origin
api.interceptors.request.use(config => {
    console.log('Request origin:', window.location.origin);
    console.log('Request URL:', config.url);
    return config;
});
```

## Production Considerations

### 1. Secure CORS Configuration
For production, use strict origin checking:
```javascript
const allowedOrigins = [
    'https://prouj.vercel.app',
    'https://your-custom-domain.com'
];
```

### 2. Environment Variables
Set production origins in your hosting platform:
```env
ALLOWED_ORIGINS=https://prouj.vercel.app,https://your-domain.com
NODE_ENV=production
```

### 3. Security Headers
Add additional security headers:
```javascript
app.use(helmet({
    crossOriginEmbedderPolicy: false,
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));
```

## Next Steps

1. **Restart your server** with the new configuration
2. **Check browser console** for any remaining CORS errors
3. **Verify the exact origin** being sent by your frontend
4. **Test multiple endpoints** to ensure consistent behavior
5. **Check server logs** for CORS debugging information

If you're still getting CORS errors after these changes, the issue might be:
- Browser cache (try incognito mode)
- Proxy/CDN configuration
- Different origin than expected (check browser DevTools)
