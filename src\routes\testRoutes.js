const express = require('express');
const router = express.Router();
const authMiddleware = require('../middlewares/authMiddleware');
const roleMiddleware = require('../middlewares/roleMiddleware');
const testController = require('../controllers/testController');
const {
    verifyTestOwnership,
    verifyCandidateTestAccess,
    verifyJobApplication,
    testOperationRateLimit,
    sanitizeTestInput
} = require('../middlewares/testSecurityMiddleware');
const {
    validateCreateTest,
    validateUpdateTest,
    validateAssignCandidates,
    validateQuestionBundle,
    validateFilterQuestions,
    validateSearchCandidates,
    validateObjectId
} = require('../middlewares/testValidation');

// Apply authentication middleware
router.use(authMiddleware);


//  Company & Admin Routes

router.use(roleMiddleware(['company', 'admin']));

//  Create test
router.post('/', validateCreateTest, sanitizeTestInput, testOperationRateLimit(5, 300000), testController.createTest);

//  Validate email configuration
router.get('/email/validate', testController.validateEmailConfiguration);

//  Update test
router.put('/:testId', validateObjectId('testId'), validateUpdateTest, verifyTestOwnership, sanitizeTestInput, testController.updateTest);

//  Delete test
router.delete('/:testId', validateObjectId('testId'), verifyTestOwnership, testController.deleteTest);

router.get('/', testController.getTests);

router.get('/:testId', validateObjectId('testId'), verifyTestOwnership, testController.getTestDetails);

router.post('/:testId/assign', validateObjectId('testId'), validateAssignCandidates, verifyJobApplication, testController.assignCandidatesToTest);

//  View test results (submissions and scores)
router.get('/:testId/results', validateObjectId('testId'), verifyTestOwnership, testController.getTestResults);

router.get('/:testId/analytics', validateObjectId('testId'), verifyTestOwnership, testController.getTestAnalytics);

router.put('/:testId/participants/:participantId/feedback', validateObjectId('testId'), validateObjectId('participantId'), verifyTestOwnership, testController.updateParticipantFeedback);

router.put('/:testId/participants/:participantId/grade', validateObjectId('testId'), validateObjectId('participantId'), verifyTestOwnership, testController.manualGradeParticipant);

// ============ ENHANCED QUESTION MANAGEMENT ROUTES ============

// Get questions by category
router.get('/questions/by-category', testController.getQuestionsByCategory);

// Get all question categories
router.get('/questions/categories', testController.getQuestionCategories);

// Filter questions with multiple criteria
router.get('/questions/filter', validateFilterQuestions, testController.filterQuestions);

// Get questions for a specific test
router.get('/:testId/questions', validateObjectId('testId'), verifyTestOwnership, testController.getTestQuestions);

// Add questions to existing test
router.post('/:testId/questions', validateObjectId('testId'), verifyTestOwnership, testController.addQuestionsToTest);

// Remove questions from test
router.delete('/:testId/questions', validateObjectId('testId'), verifyTestOwnership, testController.removeQuestionsFromTest);

// ============ QUESTION BUNDLE MANAGEMENT ROUTES ============

// Create question bundle
router.post('/question-bundles', validateQuestionBundle, testOperationRateLimit(3, 300000), testController.createQuestionBundle);

// Get all question bundles
router.get('/question-bundles', testController.getQuestionBundles);

// Get specific question bundle
router.get('/question-bundles/:bundleId', validateObjectId('bundleId'), testController.getQuestionBundle);

// Update question bundle
router.put('/question-bundles/:bundleId', validateObjectId('bundleId'), validateQuestionBundle, testController.updateQuestionBundle);

// Delete question bundle
router.delete('/question-bundles/:bundleId', validateObjectId('bundleId'), testController.deleteQuestionBundle);

// ============ ENHANCED CANDIDATE MANAGEMENT ROUTES ============

// Get all candidates for company (only those who applied for jobs)
router.get('/candidates', testController.getCandidates);

// Search candidates with filters
router.get('/candidates/search', validateSearchCandidates, testController.searchCandidates);

// Get candidates available for specific test
router.get('/candidates/available-for-test', testController.getAvailableCandidatesForTest);

// Direct candidate assignment to test
router.post('/:testId/assign-candidates', validateObjectId('testId'), validateAssignCandidates, verifyJobApplication, testController.assignCandidatesDirectly);


//  Candidate Routes: Only for candidates

router.use('/candidate', roleMiddleware(['candidate']));

// View my assigned tests
router.get('/candidate/assigned', testController.getCandidateTests);

//  Start test (mark startedAt, returns questions)
router.post('/:testId/start', validateObjectId('testId'), verifyCandidateTestAccess, testController.startTest);

//  Submit test
router.post('/:testId/submit', validateObjectId('testId'), verifyCandidateTestAccess, testController.submitTest);

//  View test result (if allowed)
router.get('/:testId/result', validateObjectId('testId'), verifyCandidateTestAccess, testController.getCandidateResult);

module.exports = router;

