const { sendTestCreatedNotification, sendBulkTestAssignmentNotification } = require('./mailServices');
const logger = require('../config/logger');

/**
 * Enhanced email notification service with comprehensive error handling and logging
 */
class EmailNotificationService {
    
    /**
     * Send test creation notification with error handling
     * @param {Object} companyUser - Company user object
     * @param {Object} testDetails - Test details for notification
     * @param {Object} company - Company object
     * @returns {Object} Result object with success status and details
     */
    async sendTestCreationNotification(companyUser, testDetails, company) {
        const logContext = {
            service: 'EmailNotificationService',
            action: 'sendTestCreationNotification',
            testId: testDetails.testId,
            companyEmail: companyUser.email
        };

        try {
            if (!companyUser || !companyUser.email) {
                logger.warn('Test creation notification skipped - no company email', logContext);
                return {
                    success: false,
                    reason: 'NO_EMAIL',
                    message: 'Company email not available'
                };
            }

            const emailDetails = {
                testName: testDetails.testName,
                companyName: company.companyName || company.name || 'Your Company',
                testId: testDetails.testId,
                totalQuestions: testDetails.totalQuestions || 0,
                duration: `${testDetails.duration} minutes`,
                scheduledDate: new Date(testDetails.scheduledDate).toLocaleDateString(),
                endDate: new Date(testDetails.endDate).toLocaleDateString()
            };

            const result = await sendTestCreatedNotification(companyUser.email, emailDetails);

            if (result.success) {
                logger.info('Test creation notification sent successfully', {
                    ...logContext,
                    messageId: result.messageId
                });
                return {
                    success: true,
                    messageId: result.messageId,
                    message: 'Test creation notification sent successfully'
                };
            } else {
                logger.error('Test creation notification failed', {
                    ...logContext,
                    error: result.error
                });
                return {
                    success: false,
                    reason: 'EMAIL_SEND_FAILED',
                    error: result.error,
                    message: 'Failed to send test creation notification'
                };
            }

        } catch (error) {
            logger.error('Test creation notification error', {
                ...logContext,
                error: error.message,
                stack: error.stack
            });
            return {
                success: false,
                reason: 'UNEXPECTED_ERROR',
                error: error.message,
                message: 'Unexpected error while sending test creation notification'
            };
        }
    }

    /**
     * Send candidate assignment notifications with comprehensive error handling
     * @param {Array} candidateIds - Array of candidate IDs
     * @param {Object} test - Test object
     * @param {Object} company - Company object
     * @returns {Object} Result object with detailed statistics
     */
    async sendCandidateAssignmentNotifications(candidateIds, test, company) {
        const logContext = {
            service: 'EmailNotificationService',
            action: 'sendCandidateAssignmentNotifications',
            testId: test._id,
            candidateCount: candidateIds.length
        };

        try {
            if (!candidateIds || candidateIds.length === 0) {
                logger.info('No candidates to notify', logContext);
                return {
                    success: true,
                    totalCandidates: 0,
                    successfulEmails: 0,
                    failedEmails: 0,
                    results: []
                };
            }

            // Get candidate details
            const User = require('../models/User');
            const candidates = await User.find({
                _id: { $in: candidateIds },
                role: 'candidate'
            });

            if (candidates.length === 0) {
                logger.warn('No valid candidates found for notification', logContext);
                return {
                    success: false,
                    reason: 'NO_VALID_CANDIDATES',
                    totalCandidates: candidateIds.length,
                    successfulEmails: 0,
                    failedEmails: candidateIds.length,
                    results: []
                };
            }

            // Prepare email assignments
            const emailAssignments = candidates.map(candidate => ({
                candidateEmail: candidate.email,
                candidateName: candidate.name,
                testId: test._id,
                testName: test.testName,
                duration: test.duration,
                instructions: test.instructions || '',
                companyName: company.companyName || company.name || 'Company',
                scheduledDate: test.scheduledDate,
                endDate: test.endDate
            }));

            // Send bulk notifications
            const emailResults = await sendBulkTestAssignmentNotification(emailAssignments);
            
            const successfulEmails = emailResults.filter(result => result.success).length;
            const failedEmails = emailResults.filter(result => !result.success);
            
            // Log detailed results
            logger.info('Candidate assignment notifications completed', {
                ...logContext,
                totalCandidates: candidates.length,
                successfulEmails,
                failedEmailsCount: failedEmails.length,
                successRate: `${((successfulEmails / candidates.length) * 100).toFixed(1)}%`
            });

            // Log failed emails for debugging
            if (failedEmails.length > 0) {
                logger.error('Some candidate assignment notifications failed', {
                    ...logContext,
                    failedEmails: failedEmails.map(result => ({
                        email: result.candidateEmail,
                        error: result.error
                    }))
                });
            }

            return {
                success: successfulEmails > 0,
                totalCandidates: candidates.length,
                successfulEmails,
                failedEmails: failedEmails.length,
                results: emailResults,
                message: `${successfulEmails}/${candidates.length} notifications sent successfully`
            };

        } catch (error) {
            logger.error('Candidate assignment notification error', {
                ...logContext,
                error: error.message,
                stack: error.stack
            });
            return {
                success: false,
                reason: 'UNEXPECTED_ERROR',
                error: error.message,
                totalCandidates: candidateIds.length,
                successfulEmails: 0,
                failedEmails: candidateIds.length,
                results: [],
                message: 'Unexpected error while sending candidate assignment notifications'
            };
        }
    }

    /**
     * Validate email configuration and connectivity
     * @returns {Object} Validation result
     */
    async validateEmailConfiguration() {
        try {
            const nodemailer = require('nodemailer');
            const transporter = nodemailer.createTransporter({
                host: "smtp.gmail.com",
                port: 587,
                secure: false,
                auth: {
                    user: process.env.EMAIL_USER || "<EMAIL>",
                    pass: process.env.EMAIL_PASS || "towb ekvx nott fzbd",
                },
            });

            await transporter.verify();
            
            logger.info('Email configuration validated successfully');
            return {
                success: true,
                message: 'Email configuration is valid'
            };
        } catch (error) {
            logger.error('Email configuration validation failed', {
                error: error.message
            });
            return {
                success: false,
                error: error.message,
                message: 'Email configuration validation failed'
            };
        }
    }
}

module.exports = new EmailNotificationService();
