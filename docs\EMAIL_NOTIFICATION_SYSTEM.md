# Email Notification System for Test Assignments

## Overview

The email notification system automatically sends email notifications to candidates and companies during the test assignment workflow. This system ensures that all stakeholders are informed about test creation and assignment activities.

## Features

### 1. Test Creation Notifications
- **Trigger**: When a company creates a new test
- **Recipient**: Company user who created the test
- **Content**: Test details, configuration summary, and next steps
- **Test URL Format**: `https://localhost:5173/testapplication/:testId`

### 2. Candidate Assignment Notifications
- **Trigger**: When candidates are assigned to a test
- **Recipients**: All newly assigned candidates
- **Content**: Personalized test invitation with test link and instructions
- **Features**: Bulk email support, personalization, error handling

### 3. Email Configuration Validation
- **Endpoint**: `GET /api/tests/email/validate`
- **Purpose**: Verify SMTP configuration and connectivity
- **Access**: Company and admin users only

## Architecture

### Core Components

1. **mailServices.js** - Core email sending functionality
2. **emailNotificationService.js** - Enhanced service with error handling and logging
3. **testController.js** - Integration points for email notifications

### Email Templates

The system uses professional HTML email templates with:
- Company branding and logo
- Responsive design
- Clear call-to-action buttons
- Important notes and instructions
- Professional styling

## Implementation Details

### Test Creation Workflow

```javascript
// In testController.createTest()
const emailResult = await emailNotificationService.sendTestCreationNotification(
    companyUser,
    {
        testName,
        testId: test._id,
        totalQuestions: questions.length,
        duration,
        scheduledDate,
        endDate
    },
    company
);
```

### Candidate Assignment Workflow

```javascript
// In testController.assignCandidatesToTest() and assignCandidatesDirectly()
const emailResult = await emailNotificationService.sendCandidateAssignmentNotifications(
    newlyAssignedIds,
    test,
    company
);
```

## Configuration

### Environment Variables

```env
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
SUPPORT_EMAIL=<EMAIL>
BRAND_NAME=Your Company Name
LOGO_URL=https://your-logo-url.com/logo.png
```

### SMTP Configuration

The system uses Gmail SMTP by default:
- Host: smtp.gmail.com
- Port: 587
- Security: STARTTLS
- Authentication: Username/Password

## Email Content

### Test Creation Email

**Subject**: `Test Created Successfully: [Test Name]`

**Content**:
- Test configuration summary
- Test ID and details
- Dashboard link for management
- Next steps for candidate assignment

### Candidate Assignment Email

**Subject**: `Test Assignment: [Test Name] - Action Required`

**Content**:
- Personalized greeting
- Test details and duration
- Direct test link
- Important instructions and notes
- Company information

## Error Handling

### Comprehensive Error Management

1. **Email Configuration Errors**
   - SMTP connection failures
   - Authentication issues
   - Invalid configuration

2. **Recipient Errors**
   - Invalid email addresses
   - Missing candidate information
   - Database lookup failures

3. **Sending Errors**
   - Network connectivity issues
   - Rate limiting
   - Service unavailability

### Fallback Mechanisms

- Email failures don't block core functionality
- Detailed logging for debugging
- Retry mechanisms for transient failures
- Graceful degradation

## Testing

### Unit Tests
- `src/tests/emailNotificationService.test.js`
- Comprehensive test coverage for all scenarios
- Mock-based testing to avoid sending actual emails

### Integration Tests
- `test/email-notification-integration.test.js`
- End-to-end workflow testing
- Database integration testing

### Manual Testing
- `test/manual-email-test.js`
- Real email sending verification
- Configuration validation
- Bulk notification testing

## API Endpoints

### Email Validation
```
GET /api/tests/email/validate
Authorization: Bearer <token>
Role: company, admin

Response:
{
  "success": true,
  "message": "Email configuration is valid and working",
  "details": { ... }
}
```

## Monitoring and Logging

### Log Levels

1. **INFO**: Successful operations
2. **WARN**: Non-critical issues (missing emails, etc.)
3. **ERROR**: Failed operations and errors

### Log Context

All logs include:
- Service name and action
- Test ID and candidate information
- Email addresses (for debugging)
- Error details and stack traces

### Example Log Entries

```javascript
// Successful notification
logger.info('Test creation notification sent successfully', {
    service: 'EmailNotificationService',
    action: 'sendTestCreationNotification',
    testId: '507f1f77bcf86cd799439011',
    companyEmail: '<EMAIL>',
    messageId: 'smtp-message-id'
});

// Failed notification
logger.error('Candidate assignment notification failed', {
    service: 'EmailNotificationService',
    action: 'sendCandidateAssignmentNotifications',
    testId: '507f1f77bcf86cd799439011',
    candidateCount: 5,
    failedEmails: [
        { email: '<EMAIL>', error: 'Invalid email address' }
    ]
});
```

## Security Considerations

1. **Email Address Validation**
   - Validate email formats
   - Verify recipient permissions
   - Prevent email injection

2. **Rate Limiting**
   - Prevent spam and abuse
   - Implement delays between bulk emails
   - Monitor sending patterns

3. **Data Privacy**
   - Don't log sensitive information
   - Secure email credentials
   - Comply with privacy regulations

## Troubleshooting

### Common Issues

1. **SMTP Authentication Failures**
   - Check email credentials
   - Verify app password for Gmail
   - Ensure 2FA is enabled

2. **Email Not Received**
   - Check spam/junk folders
   - Verify email address validity
   - Check email service logs

3. **Template Rendering Issues**
   - Verify template variables
   - Check HTML syntax
   - Test with different email clients

### Debugging Steps

1. Run email configuration validation
2. Check application logs
3. Test with manual email script
4. Verify SMTP connectivity
5. Check email service status

## Future Enhancements

1. **Email Templates**
   - Multiple template themes
   - Customizable branding
   - Multi-language support

2. **Delivery Tracking**
   - Email open tracking
   - Click tracking
   - Delivery confirmations

3. **Advanced Features**
   - Email scheduling
   - Reminder notifications
   - Custom email content

4. **Integration Options**
   - Multiple email providers
   - SMS notifications
   - Push notifications
